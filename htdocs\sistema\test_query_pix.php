<?php
/**
 * Script para testar a query PIX dos relatórios
 * Acesse via navegador: http://localhost:8010/sistema/test_query_pix.php
 */

// Configuração da base de dados
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sis_nfe_pdv';

header('Content-Type: text/html; charset=utf-8');
echo "<h2>Teste da Query PIX - Relatórios</h2>";
echo "<pre>";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Conectado à base de dados com sucesso.\n\n";
    
    // Testar a query exata que está sendo usada no relatório
    echo "1. Testando query do relatório com subquery PIX:\n";
    echo "=================================================\n";
    
    $query = "
        SELECT 
            date as abertura, 
            closed_at as fechamento, 
            CONCAT(u.first_name, ' ', u.last_name) as user, 
            cash_in_hand as caixa_inicial, 
            total_stripe as debito, 
            total_cc as credito, 
            total_cash as dinheiro, 
            (SELECT COALESCE(SUM(p.amount), 0.00) FROM tec_payments p
             WHERE p.paid_by = 'pix'
             AND p.created_by = r.user_id
             AND p.date >= r.date
             AND p.date <= COALESCE(r.closed_at, NOW())) as pix,
            (cash_in_hand + total_cash) as saldo, 
            total_reforco as reforco, 
            total_sangrias as sangria, 
            note as informacoes
        FROM tec_registers r
        LEFT JOIN tec_users u ON u.id = r.user_id
        ORDER BY date DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->query($query);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($results as $row) {
        echo "Usuário: " . $row['user'] . "\n";
        echo "Abertura: " . $row['abertura'] . "\n";
        echo "Fechamento: " . ($row['fechamento'] ? $row['fechamento'] : 'Em aberto') . "\n";
        echo "Dinheiro: R$ " . number_format($row['dinheiro'], 2, ',', '.') . "\n";
        echo "PIX: R$ " . number_format($row['pix'], 2, ',', '.') . "\n";
        echo "Saldo: R$ " . number_format($row['saldo'], 2, ',', '.') . "\n";
        echo "---\n";
    }
    
    // Verificar se há pagamentos PIX
    echo "\n2. Verificando pagamentos PIX na base:\n";
    echo "=====================================\n";
    
    $stmt = $pdo->query("
        SELECT 
            p.id,
            p.date,
            p.amount,
            p.created_by,
            u.first_name,
            s.id as sale_id
        FROM tec_payments p
        LEFT JOIN tec_users u ON u.id = p.created_by
        LEFT JOIN tec_sales s ON s.id = p.sale_id
        WHERE p.paid_by = 'pix'
        ORDER BY p.date DESC
        LIMIT 10
    ");
    
    $pix_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($pix_payments) > 0) {
        echo "Encontrados " . count($pix_payments) . " pagamentos PIX:\n";
        foreach ($pix_payments as $payment) {
            echo "ID: " . $payment['id'] . " | ";
            echo "Data: " . $payment['date'] . " | ";
            echo "Valor: R$ " . number_format($payment['amount'], 2, ',', '.') . " | ";
            echo "Usuário: " . $payment['first_name'] . " | ";
            echo "Venda: " . $payment['sale_id'] . "\n";
        }
    } else {
        echo "Nenhum pagamento PIX encontrado!\n";
    }
    
    // Verificar registros de caixa
    echo "\n3. Verificando registros de caixa:\n";
    echo "==================================\n";
    
    $stmt = $pdo->query("
        SELECT 
            r.id,
            r.date,
            r.closed_at,
            r.user_id,
            u.first_name,
            r.status
        FROM tec_registers r
        LEFT JOIN tec_users u ON u.id = r.user_id
        ORDER BY r.date DESC
        LIMIT 5
    ");
    
    $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($registers as $reg) {
        echo "ID: " . $reg['id'] . " | ";
        echo "Usuário: " . $reg['first_name'] . " | ";
        echo "Status: " . $reg['status'] . " | ";
        echo "Abertura: " . $reg['date'] . " | ";
        echo "Fechamento: " . ($reg['closed_at'] ? $reg['closed_at'] : 'Em aberto') . "\n";
    }
    
    echo "\n✓ Teste concluído!\n";
    echo "\n<strong>Acesse os relatórios em:</strong>\n";
    echo "<a href='reports/registers' target='_blank'>http://localhost:8010/sistema/reports/registers</a>\n";
    
} catch (PDOException $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
